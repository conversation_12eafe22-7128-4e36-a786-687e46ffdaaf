from enum import IntEnum
from typing import Optional, List
from pydantic import BaseModel

from core.schema.chat_base import Item, MessageType
from util.string_util import to_json_str


class EventType(IntEnum):
    START_EVENT = 1
    TEXT_CHUNK_EVENT = 2
    FINISH_EVENT = 3

    def __repr__(self):
        return str(self.value)


class ChatResponseData(BaseModel):
    answer_type: Optional[MessageType] = MessageType.TEXT
    model_version: str = ""
    answer_start_time: int = 0
    request_receive_time: int = 0
    prompt: Optional[str] = None
    text: Optional[str] = ""
    selected_item: Optional[Item] = None
    item_list: Optional[List[Item]] = None
    answer_finish_time: Optional[int] = 0
    total_tokens: Optional[int] = 0
    time_cost: Optional[str] = ""

    def to_dict(self):
        return {k: v for k, v in self.model_dump().items() if v is not None}

    @classmethod
    def from_dict(cls, data_dict: dict):
        # Handle answer_type conversion from int to MessageType enum if needed
        if "answer_type" in data_dict and isinstance(data_dict["answer_type"], int):
            data_dict["answer_type"] = MessageType(data_dict["answer_type"])

        # Handle selected_item conversion from dict to Item object
        if "selected_item" in data_dict and data_dict["selected_item"] is not None:
            if isinstance(data_dict["selected_item"], dict):
                data_dict["selected_item"] = Item(**data_dict["selected_item"])

        # Handle item_list conversion from list of dicts to list of Item objects
        if "item_list" in data_dict and data_dict["item_list"] is not None:
            if isinstance(data_dict["item_list"], list):
                data_dict["item_list"] = [Item(**item) if isinstance(item, dict) else item
                                          for item in data_dict["item_list"]]

        return cls(**data_dict)


class ChatResponse(BaseModel):
    event: EventType
    data: ChatResponseData
    conversation_id: str = ""
    # 生成一个 request id 给后端区分不同的 message，统一存储
    request_id: str = ""

    def __str__(self):
        return to_json_str(self)

    def __repr__(self):
        return to_json_str(self)

    def to_dict(self):
        return {
            'event': self.event.value,
            'conversation_id': self.conversation_id,
            'request_id': self.request_id,
            'data': self.data.to_dict()
        }

    @classmethod
    def from_dict(cls, response_dict: dict):
        # Make a copy to avoid modifying the original dict
        data = response_dict.copy()

        # Handle event conversion from int to EventType enum
        if "event" in data and isinstance(data["event"], int):
            data["event"] = EventType(data["event"])

        # Handle data conversion from dict to ChatResponseData object
        if "data" in data and isinstance(data["data"], dict):
            data["data"] = ChatResponseData.from_dict(data["data"])

        return cls(**data)


if __name__ == '__main__':
    # Test to_dict method
    response = ChatResponse(
        event=EventType.FINISH_EVENT,
        conversation_id="conversation_id",
        request_id="request_id",
        data=ChatResponseData(
            answer_start_time=123,
            answer_finish_time=345,
            request_receive_time=123,
            model_version="v1",
            prompt="prompt",
            answer_type=MessageType.TEXT,
            selected_item=Item(item_id="123", item_name="Xiaomi 15", category_id="smartphone",
                               category_name="Smartphone")
        )
    )

    # Convert to dict
    response_dict = response.to_dict()
    print("Original to dict:")
    print(response_dict)

    # Test from_dict method
    reconstructed_response = ChatResponse.from_dict(response_dict)
    print("\nReconstructed from dict:")
    print(reconstructed_response)

    # Test with raw dict that has integer values for enums
    raw_dict = {
        'event': 3,  # FINISH_EVENT as int
        'conversation_id': 'test_conv_id',
        'request_id': 'test_req_id',
        'data': {
            'answer_type': 1,  # TEXT as int
            'model_version': 'v2',
            'answer_start_time': 100,
            'request_receive_time': 50,
            'text': 'Hello, world!',
            'selected_item': {
                'item_id': '456',
                'item_name': 'Xiaomi 15 Ultra',
                'category_id': 'smartphone',
                'category_name': 'Smartphone'
            },
            'item_list': [
                {
                    'item_id': '456',
                    'item_name': 'Xiaomi 15 Ultra',
                    'category_id': 'smartphone',
                    'category_name': 'Smartphone'
                },
                {
                    'item_id': '789',
                    'item_name': 'Redmi 13',
                    'category_id': 'smartphone',
                    'category_name': 'Smartphone'
                }
            ]
        }
    }

    # Convert raw dict to ChatResponse object
    from_raw_dict = ChatResponse.from_dict(raw_dict)
    print("\nFrom raw dict:")
    print(from_raw_dict)
    print("\nEvent type:", from_raw_dict.event, type(from_raw_dict.event))
    print("Answer type:", from_raw_dict.data.answer_type, type(from_raw_dict.data.answer_type))
    print("Selected item:", from_raw_dict.data.selected_item, type(from_raw_dict.data.selected_item))
    print("Item list length:", len(from_raw_dict.data.item_list) if from_raw_dict.data.item_list else 0)
