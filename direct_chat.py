#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to directly call the ChatService.chat method without starting the Uvicorn server.
This allows for testing and debugging the chat functionality directly.
"""

import asyncio
import json
import sys
import uuid
from pathlib import Path

from core.validator import ChatRequestValidator
from core.processor import preprocess_request

# Get the absolute path of the script file
script_path = Path(__file__).resolve()
# Get the project root directory
project_root = script_path.parent
# Add the project root to the Python path
sys.path.append(str(project_root))

# Change the current working directory to the project root
import os

os.chdir(project_root)
print(f"Working directory set to: {os.getcwd()}")

from loguru import logger
from prometheus_client import Counter, Histogram

from config.common_config import get_env_by_key
from config.model_config import MODEL_CONFIG, ModelConfigKey
from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST, DATA_BASE_PORT, DATA_BASE_USER, \
    DATA_BASE_PASSWORD, DATA_BASE_NAME
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import ChatR<PERSON>ponse, EventType
from data_loader import load_item_name_list, load_item_param_config_dict, load_item_dataset_id_dict, \
    load_item_intro_dict, load_name_item_dict
from service.chat_service import ChatService
from service.model_manager import ModelManager
from service.prompt_build_service import PromptBuildService
from service.query_parse_service import QueryParseService
from util.common_util import get_cur_millis, decode_sse, pprint, is_empty
from util.mysql_db_manager import DBManager


class MockRequest:
    """Mock FastAPI Request object for testing purposes."""

    def __init__(self):
        self.headers = {"X-Request-ID": f"mock-{uuid.uuid4()}"}
        self.state = type('obj', (object,), {'logger': logger})

    async def is_disconnected(self):
        return False


def setup_services(chat_request: ChatRequest, key=0) -> ChatService:
    """Set up all the required services for the ChatService."""

    # Environment setup
    ENV_NAME = get_env_by_key("ENV_NAME", "local")
    if ENV_NAME == "local":
        ENV_NAME = "test"  # local uses test environment model config

    # Load configuration
    CUR_CONFIG = MODEL_CONFIG[ENV_NAME]

    # Load data
    ITEM_NAME_LIST, NORMALIZED_ITEM_NAME_LIST = load_item_name_list(CUR_CONFIG[ModelConfigKey.ITEM_ID_NAME_PATH])
    ITEM_NAME_XIAOMI_LIST, NORMALIZED_ITEM_NAME_XIAOMI_LIST = load_item_name_list(
        CUR_CONFIG[ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH])
    ITEM_NAME_DATASET_ID_DICT = load_item_dataset_id_dict(CUR_CONFIG[ModelConfigKey.ITEM_NAME_DATASET_ID_PATH])
    ITEM_NAME_INTRO_DICT = load_item_intro_dict(CUR_CONFIG[ModelConfigKey.MINET_INTRO_PATH])
    ITEM_PARAM_CONFIG_DICT = load_item_param_config_dict(CUR_CONFIG[ModelConfigKey.MINET_PARAM_PATH])
    ITEM_NAME_ID_CLS_ITEM = load_name_item_dict(CUR_CONFIG[ModelConfigKey.ITEM_ID_NAME_PATH])

    # Create metrics (using dummy metrics for testing)
    ENDPOINT_CALL_COUNTER = Counter(
        f"global_copilot_counter_{key}",
        "Total calls to endpoint",
        ["object", "condition"],
    )

    TIMER_HISTROGRAM = Histogram(
        name=f"global_copilot_timer_{key}",
        documentation="Latency of endpoint calls",
        labelnames=["object", "condition"],
        buckets=[0, 50, 100, 150, 200, 400, 800, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 20000,
                 30000],
    )

    # Create request-specific logger
    context_logger = logger.bind(request_id="direct-chat", chat_request_id=chat_request.request_id)

    # Initialize request time
    request_receive_time = get_cur_millis()

    # Initialize database manager
    db_manager = DBManager(
        host=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_HOST],
        user=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_USER],
        password=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PASSWORD],
        database=RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_NAME],
        port=int(RUN_CONFIG_DICT[ENV_NAME][DATA_BASE_PORT])
    )

    # Initialize model manager
    model_manager = ModelManager(
        context_logger,
        ENDPOINT_CALL_COUNTER,
        TIMER_HISTROGRAM,
        CUR_CONFIG[ModelConfigKey.DATASET_KEY],
    )

    # Initialize query parse service
    query_parse_service = QueryParseService(
        context_logger,
        CUR_CONFIG[ModelConfigKey.API_KEY_JSON],
        CUR_CONFIG[ModelConfigKey.API_KEY_JSON_MIFY2],
        CUR_CONFIG[ModelConfigKey.API_KEY_JSON_MIFY3],
        model_manager,
        ITEM_NAME_LIST,
        NORMALIZED_ITEM_NAME_LIST,
        ITEM_NAME_XIAOMI_LIST
    )

    # Initialize prompt build service
    prompt_build_service = PromptBuildService(
        context_logger,
        ITEM_PARAM_CONFIG_DICT,
        model_manager,
        ITEM_NAME_DATASET_ID_DICT,
        ITEM_NAME_INTRO_DICT
    )

    # Initialize chat service
    chat_service = ChatService(
        context_logger,
        ENDPOINT_CALL_COUNTER,
        TIMER_HISTROGRAM,
        CUR_CONFIG[ModelConfigKey.API_KEY_TEXT],
        query_parse_service,
        prompt_build_service,
        model_manager,
        NORMALIZED_ITEM_NAME_LIST,
        ITEM_NAME_XIAOMI_LIST,
        db_manager,
        ITEM_NAME_ID_CLS_ITEM,
    )
    return chat_service


async def process_chat_response(chat_service: ChatService, chat_request: ChatRequest) -> ChatResponse:
    ChatRequestValidator(logger).validate_chat_request(chat_request)
    preprocess_request(chat_request)
    final_response = None
    async for chunk in chat_service.chat(chat_request, MockRequest()):
        response_dict = decode_sse(chunk)
        if is_empty(response_dict) or "event" not in response_dict:
            continue

        response = ChatResponse.from_dict(response_dict)
        if response.event == EventType.FINISH_EVENT:
            print(f"chat response")
            pprint(response.to_dict())
            final_response = response
    return final_response


async def direct_chat_to_copilot(chat_request, key=0) -> ChatResponse:
    """Main function to run the direct chat test.

    Args:
        chat_request: ChatRequest object containing the request details

    Returns:
        ChatResponse: The final response from the chat service
    """
    print(f"\n=== Direct Chat Test ===")
    ChatRequestValidator(logger).validate_chat_request(chat_request)
    # Set up services
    chat_service = setup_services(chat_request, key)

    print("Services initialized. Sending request...\n")

    # Process the chat response and get the final response
    final_response = await process_chat_response(chat_service, chat_request)

    print("\n=== Test Complete ===")

    return final_response


def direct_chat_to_copilot_from_file(path, key=0):
    with open(path, "r") as f:
        data = json.load(f)
    chat_request = ChatRequest.from_dict(data)
    result = asyncio.run(direct_chat_to_copilot(chat_request, key))
    pprint(result)
    return result


if __name__ == "__main__":
    # Run the async main function
    path = "/Users/<USER>/workspace/inference/tmp.json"
    with open(path, "r") as f:
        data = json.load(f)
    chat_request = ChatRequest.from_dict(data)
    asyncio.run(direct_chat_to_copilot(chat_request))
